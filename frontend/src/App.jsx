import React from 'react';
import { BrowserRouter as Router, Routes, Route, useNavigate, useLocation } from 'react-router-dom';
import PdfViewer from './components/PdfViewer';
import PropertyAnalysis from './components/PropertyAnalysis';
import RoomTypeAnalysis from './components/RoomTypeAnalysis';
import { useState, useEffect } from 'react';
import EnhancedPdfViewer from './components/EnhancedPdfViewer';
import { logEvent, logError } from './UsageLogger';
import { modelOptions } from './modelOptions';



function App() {
    const [files, setFiles] = React.useState([]);
    const [file, setFile] = React.useState(null);
    const [processing, setProcessing] = React.useState(false);
    const [autoProcessing, setAutoProcessing] = React.useState(false);
    const [userDismissedReselectionError, setUserDismissedReselectionError] = React.useState(false);
    const [taskStatus, setTaskStatus] = React.useState(null);
    const [taskIds, setTaskIds] = React.useState([]);
    const [taskId, setTaskId] = React.useState(null);
    const [results, setResults] = React.useState([]);
    const [result, setResult] = React.useState(null);
    const [error, setError] = React.useState(null);
    const [documentInfo, setDocumentInfo] = React.useState(null);
    const [pdfUrl, setPdfUrlState] = React.useState(null);

    // Wrapper to log PDF URL changes
    const setPdfUrl = (url) => {
        console.log('setPdfUrl called with:', url);
        console.trace('setPdfUrl call stack');
        setPdfUrlState(url);
    };
    const [endpointStatus, setEndpointStatus] = React.useState({});
    const [editingProperties, setEditingProperties] = React.useState(false);
    const [propertyList, setPropertyList] = React.useState([]);
    const [newProperty, setNewProperty] = React.useState('');
    const [editingPeriods, setEditingPeriods] = React.useState(false);
    const [periodList, setPeriodList] = React.useState([]);
    const [newPeriod, setNewPeriod] = React.useState('');
    const [editingRateTypes, setEditingRateTypes] = React.useState(false);
    const [rateTypeList, setRateTypeList] = React.useState([]);
    const [newRateType, setNewRateType] = React.useState('');
    const [selectedModel, setSelectedModel] = React.useState('gemini-2.0-flash');
    const [sections, setSections] = React.useState([]);
    const [selectedSection, setSelectedSection] = React.useState(null);
    const [sectionResults, setSectionResults] = React.useState({});
    const propertyNamesRef = React.useRef(null);
    const [selectedRawSection, setSelectedRawSection] = React.useState(null);
    const [rawText, setRawText] = React.useState(null);
    const [loadingRawText, setLoadingRawText] = React.useState(false);
    const navigate = useNavigate();
    const location = useLocation();
    const [editingValidity, setEditingValidity] = React.useState(false);
    const [currentValidityData, setCurrentValidityData] = React.useState({ valid: true });
    const [editingOverarchingPeriod, setEditingOverarchingPeriod] = React.useState(false);
    const [currentOverarchingPeriodData, setCurrentOverarchingPeriodData] = React.useState({ start_date: '', end_date: '' });
    const [editingSTOInfo, setEditingSTOInfo] = React.useState(false);
    const [currentSTOInfoData, setCurrentSTOInfoData] = React.useState({ has_sto: false });

    // Helper function to format dates as dd/mm/yyyy
    const formatDateToDDMMYYYY = (dateStr) => {
        if (!dateStr) return 'N/A';
        const date = new Date(dateStr);
        if (isNaN(date.getTime())) {
            return 'Invalid Date';
        }
        const day = String(date.getDate()).padStart(2, '0');
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const year = date.getFullYear();
        return `${day}/${month}/${year}`;
    };

     React.useEffect(() => {
        // Check if we're coming from a workflow that needs file reselection
        if (location.state?.needsFileReselection && !userDismissedReselectionError) {
            setError(location.state.message);
            setFile(null);
            setFiles([]);
            setPdfUrl(null);
            setSelectedSection(null);
            setSections([]);
            setSectionResults({});
            // Don't remove pendingFiles yet - we need them for resume logic
            // Store the remaining files from navigation state if available
            if (location.state.remainingFiles) {
                localStorage.setItem('pendingFiles', JSON.stringify(location.state.remainingFiles));
            }
            localStorage.removeItem('workflowState');
            localStorage.removeItem('processNextFile');
            return;
        }

        // Check if a file is being passed via location.state (occurs on navigation from property analysis)
        // or if a file is already set (e.g., user selected one before navigating away and back to root in the same session without refresh)
        if (!location.state?.file && !file) {
            // Check if we should process the next file
            const processNextFile = localStorage.getItem('processNextFile');
            const pendingFilesStr = localStorage.getItem('pendingFiles');
            
            if (processNextFile === 'true' && pendingFilesStr) {
    try {
        const pendingFiles = JSON.parse(pendingFilesStr);
        if (pendingFiles.length > 0) {
            console.log(`Loading next file from pending files: ${pendingFiles[0]}`);
            // We need to find the file in the input element
            const fileInput = document.querySelector('input[type="file"]');
            if (fileInput && fileInput.files && fileInput.files.length > 0) {
                const filesArray = Array.from(fileInput.files);
                const nextFileName = pendingFiles[0];
                const nextFile = filesArray.find(f => f.name === nextFileName);
                
                if (nextFile) {
                    // Set the file and URL
                    setFile(nextFile);
                    const fileUrl = URL.createObjectURL(nextFile);
                    setPdfUrl(fileUrl);

                    // Clear previous document state to avoid conflicts
                    setSelectedSection(null);
                    setSections([]);
                    setSectionResults({});
                    setError(null);
                    setResult(null);

                    // Update the files array to reflect the remaining files
                    // Remove the current file (first in pendingFiles) and keep only the remaining pending files
                    const remainingFiles = filesArray.filter(f => pendingFiles.includes(f.name) && f.name !== pendingFiles[0]);
                    setFiles(remainingFiles);

                    // Update the pending files list to remove the current file
                    const updatedPendingFiles = pendingFiles.slice(1); // Remove first file
                    if (updatedPendingFiles.length > 0) {
                        localStorage.setItem('pendingFiles', JSON.stringify(updatedPendingFiles));
                    } else {
                        localStorage.removeItem('pendingFiles');
                    }

                    // Automatically submit the file for analysis
                    setTimeout(() => {
                        const submitEvent = { preventDefault: () => {} };
                        handleSubmit(submitEvent);
                    }, 1000); // Small delay to ensure UI is updated
                } else {
                    // File not found in input, prompt user to reselect files
                    console.log("File not found in input, prompt user to reselect files")
                    const remainingFileCount = pendingFiles.length;
                    setError(`Workflow interrupted: Please reselect your PDF files to continue processing the remaining ${remainingFileCount} file(s): ${pendingFiles.join(', ')}`);
                    setFile(null);
                    setFiles([]);
                    setPdfUrl(null);
                    setSelectedSection(null);
                    setSections([]);
                    setSectionResults({});
                    localStorage.removeItem('pendingFiles');
                    localStorage.removeItem('workflowState');
                }
            } else {
                // No files in input, prompt user to reselect
                console.log("No files in input")
                const remainingFileCount = pendingFiles.length;
                setError(`Workflow interrupted: Please reselect your PDF files to continue processing the remaining ${remainingFileCount} file(s): ${pendingFiles.join(', ')}`);
                setFile(null);
                setFiles([]);
                setPdfUrl(null);
                setSelectedSection(null);
                setSections([]);
                setSectionResults({});
                localStorage.removeItem('pendingFiles');
                localStorage.removeItem('workflowState');
            }
        }
    } catch (err) {
        console.error('Error processing next file:', err);
    }
    // Clear the flag
    localStorage.removeItem('processNextFile');
            } else {
                localStorage.removeItem('workflowState');
                localStorage.removeItem('selectedModel');
                console.log('Cleared workflowState and selectedModel from localStorage on initial fresh load.');
            }
        } else {
            console.log('Preserved workflowState and selectedModel in localStorage due to navigation or existing file.');
        }
    }, [location.state, file]); // Add location.state and file as dependencies

      // New state for search functionality
    const [searchText, setSearchText] = useState('');
    const [searchStatus, setSearchStatus] = useState(null); // 'success', 'not-found', 'error', or null
    

    const baseUrl = '/api';
    localStorage.setItem('baseUrl', baseUrl);

    console.log("Api url:", baseUrl);

    // Handler for property click
    const handlePropertyClick = (propertyName) => {
        setSearchText(propertyName);
        setSearchStatus('searching');
    };
    
    // Handler for search result callback from EnhancedPdfViewer
    const handleSearchResult = (status) => {
        setSearchStatus(status);
    };

    const handleFileChange = (event) => {
        const selectedFiles = Array.from(event.target.files);
        console.log('handleFileChange called with files:', selectedFiles.map(f => f.name));

        if (selectedFiles.length === 0) {
            setError('Please select at least one PDF file');
            setFile(null);
            setFiles([]);
            setPdfUrl(null);
            return;
        }
        
        // Check if all files are PDFs
        const allPdfs = selectedFiles.every(file => file.type === 'application/pdf');
        
        if (allPdfs) {
            setFiles(selectedFiles);
            setError(null);

            // Check if we're resuming a workflow with pending files first
            const pendingFilesStr = localStorage.getItem('pendingFiles');
            console.log('Checking for pending files:', pendingFilesStr);
            if (pendingFilesStr) {
                try {
                    const pendingFiles = JSON.parse(pendingFilesStr);
                    const selectedFileNames = selectedFiles.map(f => f.name);
                    console.log('Pending files:', pendingFiles);
                    console.log('Selected file names:', selectedFileNames);

                    // Check if the selected files contain all the pending files
                    const containsAllPending = pendingFiles.every(pendingFile =>
                        selectedFileNames.includes(pendingFile)
                    );
                    console.log('Contains all pending files:', containsAllPending);

                    if (containsAllPending && pendingFiles.length > 0) {
                        // Resume workflow with the next pending file
                        const nextFileName = pendingFiles[0];
                        const nextFile = selectedFiles.find(f => f.name === nextFileName);
                        console.log('Next file to process:', nextFileName);
                        console.log('Found next file object:', !!nextFile);

                        if (nextFile) {
                            console.log(`Resuming workflow with file: ${nextFileName}`);
                            setFile(nextFile);
                            const nextFileUrl = URL.createObjectURL(nextFile);
                            console.log('Setting PDF URL for resume:', nextFileUrl);
                            setPdfUrl(nextFileUrl);

                            // Clear the error message since we're resuming
                            setError(null);

                            // Update pending files to remove the current one
                            const updatedPendingFiles = pendingFiles.slice(1);
                            if (updatedPendingFiles.length > 0) {
                                localStorage.setItem('pendingFiles', JSON.stringify(updatedPendingFiles));
                            } else {
                                localStorage.removeItem('pendingFiles');
                            }

                            // Auto-submit for analysis - use the nextFile directly instead of relying on state
                            setTimeout(() => {
                                handleSubmitWithFile(nextFile);
                            }, 1000);

                            return; // Exit early to avoid setting up new workflow
                        }
                    }
                } catch (err) {
                    console.error('Error checking pending files:', err);
                }
            }

            // If we're not resuming a workflow, set up the first file for preview
            if (!pendingFilesStr) {
                setFile(selectedFiles[0]); // Keep the first file as the current file for backward compatibility
                // Set the URL for the first file for preview
                const fileUrl = URL.createObjectURL(selectedFiles[0]);
                setPdfUrl(fileUrl);
            }

            // Store the files in localStorage for sequential processing
            // We can't store File objects directly, so we'll just remember we have pending files
            if (selectedFiles.length > 1) {
                localStorage.setItem('pendingFiles', JSON.stringify(selectedFiles.map(f => f.name)));
            } else {
                localStorage.removeItem('pendingFiles');
            }
            
            // Log the event
            logEvent('file_selection', 'App.jsx', `Files selected: ${selectedFiles.length}`, { 
                file_count: selectedFiles.length,
                file_names: selectedFiles.map(f => f.name).join(', ')
            });
        } else {
            setError('Please select only PDF files');
            setFile(null);
            setFiles([]);
            setPdfUrl(null);
            localStorage.removeItem('pendingFiles');
            logEvent('file_selection_error', 'App.jsx', 'Invalid file type selected', { 
                error_message: 'Please select only PDF files' 
            });
        }
    };

    const fetchDocumentInfo = async (filename) => {
        try {
            // First, check if the section is valid
            console.log("getting validity...");
            const validityResponse = await fetch(`${baseUrl}/get-valid`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ 
                    filename: filename,
                    model: selectedModel 
                }),
            });

            if (!validityResponse.ok) {
                throw new Error(`HTTP error! status: ${validityResponse.status}`);
            }

            const validityData = await validityResponse.json();
            console.log(validityData);
            
            // Update section results with validity check
            setSectionResults(prev => ({
                ...prev,
                [filename]: {
                    ...prev[filename],
                    isValid: validityData
                }
            }));

            // Initialize endpoint status
            setEndpointStatus({
                isValid: { status: 'completed', label: 'Document Validity' }
            });

            // If the section is not valid, stop here
            if (!validityData.valid) {
                setEndpointStatus(prev => ({
                    ...prev,
                    propertyNames: { status: 'pending', label: 'Property Names' },
                    overarchingPeriod: { status: 'pending', label: 'Overarching Period' },
                    periods: { status: 'pending', label: 'Periods' },
                    rateTypes: { status: 'pending', label: 'Rate Types' },
                    hasSTO: { status: 'pending', label: 'STO Information' }
                }));
                logEvent('document_section_invalid', 'App.jsx', `Section ${filename} determined as invalid.`, { section_filename: filename, model: selectedModel });
                return;
            }

            // Only initialize CSV if this is the first section (index 0)
            const savedWorkflowState = localStorage.getItem('workflowState');
            if (savedWorkflowState) {
                const workflowState = JSON.parse(savedWorkflowState);
                if (workflowState.currentSectionIndex === 0) {
                    // Fetch the CSV file ID from the initialize_csv endpoint
                    const csvResponse = await fetch(`${baseUrl}/initialize_csv`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            filename: filename,
                            model: selectedModel
                        }),
                    });

                    if (!csvResponse.ok) {
                        throw new Error(`HTTP error! status: ${csvResponse.status}`);
                    }

                    const csvData = await csvResponse.json();
                    console.log("csvData", csvData);
                    const csv_file_id = csvData.file_id;
                    console.log("csvFileID", csv_file_id);

                    // Save the CSV file ID to localStorage
                    localStorage.setItem('csvFileId', csv_file_id);
                }
            }

            // If valid, proceed with fetching other data
            const endpoints = [
                { name: 'propertyNames', url: '/get-property-names', label: 'Property Names' },
                { name: 'overarchingPeriod', url: '/get-overarching-period', label: 'Overarching Period' },
                { name: 'periods', url: '/get-periods', label: 'Periods' },
                { name: 'rateTypes', url: '/general-has-weekly_rates', label: 'Rate Types' },
                { name: 'hasSTO', url: '/general-has-sto', label: 'STO Information' },
            ];

            // Update endpoint status for remaining endpoints
            setEndpointStatus(prev => ({
                ...prev,
                ...endpoints.reduce((acc, endpoint) => {
                    acc[endpoint.name] = { status: 'loading', label: endpoint.label };
                    return acc;
                }, {})
            }));

            // Process remaining endpoints in parallel
            const promises = endpoints.map(async (endpoint) => {
                try {
                    const response = await fetch(`${baseUrl}${endpoint.url}`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({ 
                            filename: filename,
                            model: selectedModel 
                        }),
                    });

                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }

                    const data = await response.json();
                    
                    // Update section results
                    setSectionResults(prev => ({
                        ...prev,
                        [filename]: {
                            ...prev[filename],
                            [endpoint.name]: data
                        }
                    }));
                    
                    // Update status to completed
                    setEndpointStatus(prevStatus => ({
                        ...prevStatus,
                        [endpoint.name]: { ...prevStatus[endpoint.name], status: 'completed' }
                    }));
                    
                    return { name: endpoint.name, data };
                } catch (err) {
                    // Update status to error
                    setEndpointStatus(prevStatus => ({
                        ...prevStatus,
                        [endpoint.name]: { ...prevStatus[endpoint.name], status: 'error', message: err.message }
                    }));
                    
                    throw err;
                }
            });

            await Promise.allSettled(promises);
            logEvent('document_info_fetched', 'App.jsx', `Successfully fetched all general info for section ${filename}`, { section_filename: filename, model: selectedModel });
            
        } catch (err) {
            setError(err.message);
            logError(`Error in fetchDocumentInfo for section ${filename}: ${err.message}`, 'App.jsx', { section_filename: filename, model: selectedModel, stack: err.stack });
        } finally {
            setProcessing(false);
        }
    };

    // Helper function to handle submit with a specific file (used for auto-resume)
    const handleSubmitWithFile = async (fileToSubmit) => {
        if (!fileToSubmit) {
            setError('Please select a file first');
            return;
        }

        setProcessing(true);
        setError(null);
        setResult(null);
        setSections([]);
        setSectionResults({});
        // Don't clear pdfUrl here since we want to keep the PDF visible during processing

        const formData = new FormData();
        formData.append('file', fileToSubmit);

        try {
            logEvent('analysis_submit', 'App.jsx', 'Auto-resume analysis submitted', { file_name: fileToSubmit.name, model: selectedModel });
            // Upload the PDF file to get the filename and chunks
            console.log("Uploading pdf...");
            const uploadResponse = await fetch(`${baseUrl}/upload-pdf`, {
                method: 'POST',
                body: formData,
            });

            if (!uploadResponse.ok) {
                const errorData = await uploadResponse.json().catch(() => ({})); // Try to get error details
                logError(`Upload failed for auto-resume analysis: ${uploadResponse.status}`, 'App.jsx', { file_name: fileToSubmit.name, status_text: uploadResponse.statusText, response_body: errorData });
                throw new Error(`HTTP error! status: ${uploadResponse.status}`);
            }

            const uploadData = await uploadResponse.json();
            console.log("uploadData", uploadData);
            const { original_filename, chunk_files } = uploadData;

            // Store the sections
            setSections(chunk_files);
            setSelectedSection(chunk_files[0]); // Select first section by default

            // Clear any existing workflow state
            localStorage.removeItem('workflowState');

            // Create new workflow state starting at section 0
            const newWorkflowState = {
                documentFilename: chunk_files[0],
                model: selectedModel,
                currentSectionIndex: 0,
                sectionNames: chunk_files.map((section, index) => ({ name: section, index })),
                currentPropertyIndex: 0
            };
            localStorage.setItem('workflowState', JSON.stringify(newWorkflowState));

            // Fetch document information for the first section
            await fetchDocumentInfo(chunk_files[0]);

        } catch (err) {
            setError(err.message);
            logError(`Error in handleSubmitWithFile: ${err.message}`, 'App.jsx', { file_name: fileToSubmit ? fileToSubmit.name : 'unknown', model: selectedModel, stack: err.stack });
        } finally {
            setProcessing(false);
        }
    };

    const handleSubmit = async (event) => {
        event.preventDefault();
        if (!file) {
            setError('Please select a file first');
            return;
        }

        setProcessing(true);
        setError(null);
        setResult(null);
        setSections([]);
        setSectionResults({});
        // Don't clear pdfUrl here since we want to keep the PDF visible during processing

        const formData = new FormData();
        formData.append('file', file);

        try {
            logEvent('analysis_submit', 'App.jsx', 'Manual analysis submitted', { file_name: file.name, model: selectedModel });
            // Upload the PDF file to get the filename and chunks
            console.log("Uploading pdf...");
            const uploadResponse = await fetch(`${baseUrl}/upload-pdf`, {
                method: 'POST',
                body: formData,
            });

            if (!uploadResponse.ok) {
                const errorData = await uploadResponse.json().catch(() => ({})); // Try to get error details
                logError(`Upload failed for manual analysis: ${uploadResponse.status}`, 'App.jsx', { file_name: file.name, status_text: uploadResponse.statusText, response_body: errorData });
                throw new Error(`HTTP error! status: ${uploadResponse.status}`);
            }

            const uploadData = await uploadResponse.json();
            console.log("uploadData", uploadData);
            const { original_filename, chunk_files } = uploadData;
            
            // Store the sections
            setSections(chunk_files);
            setSelectedSection(chunk_files[0]); // Select first section by default
            
            // Clear any existing workflow state
            localStorage.removeItem('workflowState');
            
            // Create new workflow state starting at section 0
            const newWorkflowState = {
                documentFilename: chunk_files[0],
                model: selectedModel,
                currentSectionIndex: 0,
                sectionNames: chunk_files.map((section, index) => ({ name: section, index })),
                currentPropertyIndex: 0
            };
            localStorage.setItem('workflowState', JSON.stringify(newWorkflowState));
            
            // Fetch document information for the first section
            await fetchDocumentInfo(chunk_files[0]);

        } catch (err) {
            setError(err.message);
            logError(`Error in handleSubmit: ${err.message}`, 'App.jsx', { file_name: file ? file.name : 'unknown', model: selectedModel, stack: err.stack });
        } finally {
            setProcessing(false);
        }
    };

    const handleAutoProcess = async (event) => {
        event.preventDefault();
        const filesToProcess = files.length > 0 ? files : (file ? [file] : []);
        
        if (filesToProcess.length === 0) {
            setError('Please select at least one file first');
            return;
        }

        // Store the files in localStorage for sequential processing
        if (filesToProcess.length > 1) {
            localStorage.setItem('pendingFiles', JSON.stringify(filesToProcess.map(f => f.name)));
        } else {
            localStorage.removeItem('pendingFiles');
        }

        setAutoProcessing(true);
        setError(null);
        setTaskStatus('uploading');
        setTaskIds([]);
        setResults([]);

        try {
            logEvent('auto_analysis_submit', 'App.jsx', `Auto-analysis submitted for ${filesToProcess.length} files`, { 
                file_count: filesToProcess.length,
                file_names: filesToProcess.map(f => f.name).join(', '),
                model: selectedModel 
            });
            
            const newTaskIds = [];
            
            // Process each file sequentially to avoid overwhelming the server
            for (let i = 0; i < filesToProcess.length; i++) {
                const currentFile = filesToProcess[i];
                setTaskStatus(`uploading file ${i+1}/${filesToProcess.length}`);
                
                const formData = new FormData();
                formData.append('file', currentFile);

                const uploadResponse = await fetch(`${baseUrl}/upload-pdf`, {
                    method: 'POST',
                    body: formData,
                });

                if (!uploadResponse.ok) {
                    const errorData = await uploadResponse.json().catch(() => ({}));
                    logError(`Upload failed for auto-analysis: ${uploadResponse.status}`, 'App.jsx', { 
                        file_name: currentFile.name, 
                        status_text: uploadResponse.statusText, 
                        response_body: errorData 
                    });
                    throw new Error(`HTTP error uploading ${currentFile.name}: ${uploadResponse.status}`);
                }

                const uploadData = await uploadResponse.json();
                const { original_filename, chunk_files } = uploadData;
                const filename_to_process = chunk_files.length === 1 ? chunk_files[0] : original_filename;

                setTaskStatus(`processing file ${i+1}/${filesToProcess.length}`);
                
                const response = await fetch(`${baseUrl}/auto-process-document`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        filename: filename_to_process,
                        model: selectedModel
                    }),
                });

                if (response.status !== 202) {
                    const errorData = await response.json().catch(() => ({}));
                    logError(`Auto-process initiation failed: ${response.status}`, 'App.jsx', { 
                        file_name: filename_to_process, 
                        model: selectedModel, 
                        status_text: response.statusText, 
                        response_body: errorData 
                    });
                    throw new Error(`HTTP error processing ${currentFile.name}: ${response.status}`);
                }

                const data = await response.json();
                newTaskIds.push(data.task_id);
                
                // If this is the first file, also set the taskId for backward compatibility
                if (i === 0) {
                    setTaskId(data.task_id);
                }
            }
            
            setTaskIds(newTaskIds);
            setTaskStatus('processing');

        } catch (err) {
            console.error(err);
            setError(err.message);
            setAutoProcessing(false);
            setTaskStatus('failed');
            logError(`Error in handleAutoProcess: ${err.message}`, 'App.jsx', { 
                file_count: filesToProcess.length,
                model: selectedModel, 
                stack: err.stack 
            });
        }
    };

    useEffect(() => {
        // Handle single task ID (backward compatibility)
        if (taskStatus === 'processing' && taskId && (!taskIds || taskIds.length === 0)) {
            const interval = setInterval(async () => {
                try {
                    const response = await fetch(`${baseUrl}/task-status/${taskId}`);
                    const data = await response.json();

                    if (data.status === 'completed') {
                        setTaskStatus('completed');
                        setResult(data.result);
                        setAutoProcessing(false);
                        clearInterval(interval);
                        logEvent('auto_analysis_completed', 'App.jsx', `Task ${taskId} completed successfully.`, { task_id: taskId, result_file: data.result });

                        // Create a download link and trigger it
                        const a = document.createElement('a');
                        a.href = `${baseUrl}/download-auto-csv/${taskId}`;
                        a.download = data.result.split('/').pop();
                        document.body.appendChild(a);
                        a.click();
                        document.body.removeChild(a);

                    } else if (data.status === 'failed') {
                        setError(data.error);
                        setTaskStatus('failed');
                        setAutoProcessing(false);
                        clearInterval(interval);
                        logEvent('auto_analysis_failed', 'App.jsx', `Task ${taskId} failed.`, { task_id: taskId, error: data.error });
                    }
                } catch (err) {
                    setError('Failed to get task status.');
                    setTaskStatus('failed');
                    setAutoProcessing(false);
                    clearInterval(interval);
                    logError('Task status polling failed', 'App.jsx', { task_id: taskId, error_message: err.message });
                }
            }, 5000); // Poll every 5 seconds

            return () => clearInterval(interval);
        }
        
        // Handle multiple task IDs
        if (taskStatus === 'processing' && taskIds && taskIds.length > 0) {
            const completedTasksMap = new Map();
            const failedTasksMap = new Map();
            const newResults = [];
            
            const interval = setInterval(async () => {
                try {
                    let allCompleted = true;
                    let anyFailed = false;
                    let failedMessage = '';
                    
                    // Check status for each task ID
                    for (let i = 0; i < taskIds.length; i++) {
                        const currentTaskId = taskIds[i];
                        
                        // Skip already completed or failed tasks
                        if (completedTasksMap.has(currentTaskId) || failedTasksMap.has(currentTaskId)) {
                            continue;
                        }
                        
                        const response = await fetch(`${baseUrl}/task-status/${currentTaskId}`);
                        const data = await response.json();
                        
                        if (data.status === 'completed') {
                            completedTasksMap.set(currentTaskId, data.result);
                            newResults.push(data.result);
                            logEvent('auto_analysis_completed', 'App.jsx', `Task ${currentTaskId} completed successfully.`, { 
                                task_id: currentTaskId, 
                                result_file: data.result,
                                task_index: i + 1,
                                total_tasks: taskIds.length
                            });
                            
                            // Create a download link and trigger it
                            const a = document.createElement('a');
                            a.href = `${baseUrl}/download-auto-csv/${currentTaskId}`;
                            a.download = data.result.split('/').pop();
                            document.body.appendChild(a);
                            a.click();
                            document.body.removeChild(a);
                            
                        } else if (data.status === 'failed') {
                            failedTasksMap.set(currentTaskId, data.error);
                            anyFailed = true;
                            failedMessage += `Task ${i+1}: ${data.error}\n`;
                            logEvent('auto_analysis_failed', 'App.jsx', `Task ${currentTaskId} failed.`, { 
                                task_id: currentTaskId, 
                                error: data.error,
                                task_index: i + 1,
                                total_tasks: taskIds.length
                            });
                        } else {
                            // Task still in progress
                            allCompleted = false;
                        }
                    }
                    
                    // Update status based on all tasks
                    if (allCompleted || (completedTasksMap.size + failedTasksMap.size === taskIds.length)) {
                        if (anyFailed) {
                            if (completedTasksMap.size > 0) {
                                // Some tasks completed, some failed
                                setTaskStatus('partially_completed');
                                setError(`Some files failed to process: ${failedMessage}`);
                            } else {
                                // All tasks failed
                                setTaskStatus('failed');
                                setError(failedMessage);
                            }
                        } else {
                            // All tasks completed successfully
                            setTaskStatus('completed');
                        }
                        
                        setResults(newResults);
                        setAutoProcessing(false);
                        clearInterval(interval);
                    } else {
                        // Update progress status
                        const completedCount = completedTasksMap.size + failedTasksMap.size;
                        setTaskStatus(`processing ${completedCount}/${taskIds.length} files`);
                    }
                } catch (err) {
                    setError('Failed to get task status.');
                    setTaskStatus('failed');
                    setAutoProcessing(false);
                    clearInterval(interval);
                    logError('Task status polling failed', 'App.jsx', { task_ids: taskIds.join(','), error_message: err.message });
                }
            }, 5000); // Poll every 5 seconds

            return () => clearInterval(interval);
        }
    }, [taskStatus, taskId, baseUrl]);

    // Handle property editing
    const handlePropertyEdit = () => {
        const currentProperties = 
            sectionResults[selectedSection] && 
            sectionResults[selectedSection].propertyNames && 
            sectionResults[selectedSection].propertyNames.property_names 
            ? sectionResults[selectedSection].propertyNames.property_names 
            : [];
    
        // Initialize the editing state with the current properties
        setPropertyList([...currentProperties]); // Use spread to create a new array copy
        
        // Reset the new property input field
        setNewProperty(''); 
        setEditingProperties(true);
    };

    const handlePropertySave = () => {
        setEditingProperties(false);

        // 1. Prepare the updated property data structure (matching sectionResults)
        const updatedPropertyNamesData = {
            property_names: propertyList
        };

        // 2. Update sectionResults for the current section and endpoint
        setSectionResults(prevResults => {
            // Ensure the selected section and endpoint exist before updating
            if (!prevResults || !prevResults[selectedSection]) {
                console.error("Cannot save properties: sectionResults or selectedSection is invalid.");
                // Handle this case appropriately, maybe revert editingProperties or show an error
                setEditingProperties(true); // Revert to editing if save failed structurally
                return prevResults; // Return previous state if invalid
            }

            return {
                ...prevResults, // Keep results for other sections
                [selectedSection]: {
                    ...prevResults[selectedSection], // Keep results for other endpoints in this section
                    propertyNames: updatedPropertyNamesData // Update the propertyNames data
                }
            };
        });

        // 3. Update the documentInfo with the new property list
        setDocumentInfo(prevInfo => ({
            ...prevInfo,
            propertyNames: {
                property_names: propertyList
            }
        }));
    };

    const handlePropertyAdd = () => {
        if (newProperty.trim() && !propertyList.includes(newProperty.trim())) {
            setPropertyList([...propertyList, newProperty.trim()]);
            setNewProperty('');
        }
    };

    const handlePropertyRemove = (propertyToRemove) => {
        setPropertyList(propertyList.filter(prop => prop !== propertyToRemove));
    };

    const handlePropertyKeyPress = (e) => {
        if (e.key === 'Enter') {
            e.preventDefault();
            handlePropertyAdd();
        }
    };

        // Handle validity editing
    const handleValidityEdit = () => {
        const currentValidity = 
            sectionResults[selectedSection] && 
            sectionResults[selectedSection].isValid
            ? sectionResults[selectedSection].isValid
            : { valid: true }; // Default if no data
        setCurrentValidityData(currentValidity);
        setEditingValidity(true);
    };

    const handleValiditySave = () => {
        setSectionResults(prevResults => ({
            ...prevResults,
            [selectedSection]: {
                ...prevResults[selectedSection],
                isValid: currentValidityData
            }
        }));
        setEditingValidity(false);
    };

    const handleValidityChange = (newValue) => {
        setCurrentValidityData({ valid: newValue });
    };

    // Handle overarching period editing
    const handleOverarchingPeriodEdit = () => {
        const currentPeriod =
            sectionResults[selectedSection] &&
            sectionResults[selectedSection].overarchingPeriod
            ? sectionResults[selectedSection].overarchingPeriod
            : { start_date: '', end_date: '' }; // Default if no data
        setCurrentOverarchingPeriodData(currentPeriod);
        setEditingOverarchingPeriod(true);
    };

    const handleOverarchingPeriodSave = () => {
        setSectionResults(prevResults => ({
            ...prevResults,
            [selectedSection]: {
                ...prevResults[selectedSection],
                overarchingPeriod: currentOverarchingPeriodData
            }
        }));
        setEditingOverarchingPeriod(false);
    };

    const handleOverarchingPeriodChange = (field, value) => {
        setCurrentOverarchingPeriodData(prevData => ({
            ...prevData,
            [field]: value
        }));
    };

    // Handle STO Info editing
    const handleSTOInfoEdit = () => {
        const currentSTO =
            sectionResults[selectedSection] &&
            sectionResults[selectedSection].hasSTO
            ? sectionResults[selectedSection].hasSTO
            : { has_sto: false }; // Default if no data
        setCurrentSTOInfoData(currentSTO);
        setEditingSTOInfo(true);
    };

    const handleSTOInfoSave = () => {
        setSectionResults(prevResults => ({
            ...prevResults,
            [selectedSection]: {
                ...prevResults[selectedSection],
                hasSTO: currentSTOInfoData
            }
        }));
        setEditingSTOInfo(false);
    };

    const handleSTOInfoChange = (newValue) => {
        setCurrentSTOInfoData({ has_sto: newValue });
    };



    // Handle period editing
    const handlePeriodEdit = () => {
        setEditingPeriods(true);
    };

    const handlePeriodSave = () => {
        setEditingPeriods(false);
        // Update the section results with the new period list
        setSectionResults(prev => ({
            ...prev,
            [selectedSection]: {
                ...prev[selectedSection],
                periods: {
                    periods: periodList
                }
            }
        }));
    };

    const handlePeriodAdd = () => {
        if (newPeriod.trim() && !periodList.includes(newPeriod.trim())) {
            setPeriodList([...periodList, newPeriod.trim()]);
            setNewPeriod('');
        }
    };

    const handlePeriodRemove = (periodToRemove) => {
        setPeriodList(periodList.filter(period => period !== periodToRemove));
    };

    const handlePeriodKeyPress = (e) => {
        if (e.key === 'Enter') {
            e.preventDefault();
            handlePeriodAdd();
        }
    };

    // Handle rate type editing
    const handleRateTypeEdit = () => {
        setEditingRateTypes(true);
    };

    const handleRateTypeSave = () => {
        setEditingRateTypes(false);
        // Update the section results with the new rate type list
        setSectionResults(prev => ({
            ...prev,
            [selectedSection]: {
                ...prev[selectedSection],
                rateTypes: {
                    distinct_weekly_rates: rateTypeList
                }
            }
        }));
    };

    const handleRateTypeAdd = () => {
        if (newRateType.trim() && !rateTypeList.includes(newRateType.trim())) {
            setRateTypeList([...rateTypeList, newRateType.trim()]);
            setNewRateType('');
        }
    };

    const handleRateTypeRemove = (rateTypeToRemove) => {
        setRateTypeList(rateTypeList.filter(rateType => rateType !== rateTypeToRemove));
    };

    const handleRateTypeKeyPress = (e) => {
        if (e.key === 'Enter') {
            e.preventDefault();
            handleRateTypeAdd();
        }
    };

    // Change handlers for direct updating of values
    const handlePropertyChange = (index, value) => {
        const updatedProperties = [...propertyList];
        updatedProperties[index] = value;
        setPropertyList(updatedProperties);
    };

    const handlePeriodChange = (index, value) => {
        const updatedPeriods = [...periodList];
        updatedPeriods[index] = value;
        setPeriodList(updatedPeriods);
    };

    const handleRateTypeChange = (index, value) => {
        const updatedRateTypes = [...rateTypeList];
        updatedRateTypes[index] = value;
        setRateTypeList(updatedRateTypes);
    };

    // Handle navigation to property analysis
    const handlePropertyAnalysis = () => {
        // 1. Get the necessary data
        const currentSectionData = sectionResults[selectedSection];
        const finalizedPropertyList = propertyList;
        
        // The file object should be in state. If not, the user must re-select it.
        // This removes the dependency on localStorage for the PDF data.
        proceedWithPropertyAnalysis(file, currentSectionData, finalizedPropertyList);
    };

    // Helper function to handle the actual property analysis
    const proceedWithPropertyAnalysis = (pdfFile, currentSectionData, finalizedPropertyList) => {
        // Clear navigation flags from RoomTypeAnalysis
        localStorage.removeItem('cameFromRoomTypeAnalysisLastStep');
        localStorage.removeItem('completedPropertyIndex'); // Example of another related flag
        localStorage.removeItem('completedPropertyRoomTypesString'); // Example of another related flag
        localStorage.removeItem('completedPropertyTotalRoomTypesString'); // Example of another related flag


        if (!currentSectionData || finalizedPropertyList.length === 0 || !pdfFile) {
            setError("Cannot proceed: Missing section data, property list, or PDF file.");
            console.error("Workflow state error:", { currentSectionData, finalizedPropertyList, pdfFile });
            return;
        }

        // 2. Prepare workflow state object
        const workflowState = {
            documentFilename: selectedSection,
            model: selectedModel,
            properties: finalizedPropertyList,
            currentPropertyIndex: 0,
            sectionNames: sections.map((section, index) => ({ name: section, index })),
            currentSectionIndex: sections.findIndex((section) => section === selectedSection),
            totalProperties: finalizedPropertyList.length,
            periods: currentSectionData.periods,
            rateTypes: currentSectionData.rateTypes,
            overarchingPeriod: currentSectionData.overarchingPeriod,
            isValid: currentSectionData.isValid
        };

        // 3. Store workflow state in localStorage and navigate with file object
        try {
            localStorage.setItem('workflowState', JSON.stringify(workflowState));
            localStorage.setItem('selectedModel', selectedModel);
            
            console.log("Navigating to property analysis for property:", finalizedPropertyList[0]);
            // Pass the file object in the navigation state instead of using localStorage
            logEvent('navigation', 'App.jsx', 'Navigating to property analysis', { target_route: '/property-analysis', properties_count: finalizedPropertyList.length, section: selectedSection });
            navigate('/property-analysis', { state: { file: pdfFile } });

        } catch (error) {
            // This error is now less likely for quota, but good to keep for other potential issues.
            setError("Failed to save workflow state to localStorage.");
            console.error("localStorage error:", error);
        }
    };

    // Update propertyList when documentInfo changes
    React.useEffect(() => {
        if (documentInfo && documentInfo.propertyNames && documentInfo.propertyNames.property_names) {
            setPropertyList(documentInfo.propertyNames.property_names);
        }
    }, [documentInfo && documentInfo.propertyNames && documentInfo.propertyNames.property_names]);

    // Update propertyList when section results change
    React.useEffect(() => {
        if (selectedSection && 
            sectionResults[selectedSection] && 
            sectionResults[selectedSection].propertyNames && 
            sectionResults[selectedSection].propertyNames.property_names) {
            const newPropertyNames = sectionResults[selectedSection].propertyNames.property_names;
            if (JSON.stringify(newPropertyNames) !== JSON.stringify(propertyNamesRef.current)) {
                propertyNamesRef.current = newPropertyNames;
                setPropertyList(newPropertyNames);
            }
        }
    }, [selectedSection, sectionResults]);

    // Update periodList when section results change
    React.useEffect(() => {
        if (selectedSection &&
            sectionResults[selectedSection] &&
            sectionResults[selectedSection].periods &&
            sectionResults[selectedSection].periods.periods) {
            const newPeriods = sectionResults[selectedSection].periods.periods;
            setPeriodList(newPeriods);
        }
    }, [selectedSection, sectionResults]);

    // Update rateTypeList when section results change
    React.useEffect(() => {
        if (selectedSection &&
            sectionResults[selectedSection] &&
            sectionResults[selectedSection].rateTypes &&
            sectionResults[selectedSection].rateTypes.distinct_weekly_rates) {
            const newRateTypes = sectionResults[selectedSection].rateTypes.distinct_weekly_rates;
            setRateTypeList(newRateTypes);
        }
    }, [selectedSection, sectionResults]);

    // Split the workflow state restoration into two separate useEffect hooks
    React.useEffect(() => {
        const savedWorkflowState = localStorage.getItem('workflowState');
        const receivedFile = location.state?.file;

        if (receivedFile) {
            setFile(receivedFile);
            const fileUrl = URL.createObjectURL(receivedFile);
            setPdfUrl(fileUrl);
        }

        if (savedWorkflowState) {
            const workflowState = JSON.parse(savedWorkflowState);
            
            if (workflowState.sectionNames) {
                const allSections = workflowState.sectionNames.map(section => section.name);
                setSections(allSections);

                if (workflowState.currentSectionIndex !== undefined) {
                    const currentSection = workflowState.sectionNames[workflowState.currentSectionIndex];
                    if (currentSection) {
                        setSelectedSection(currentSection.name);
                    }
                }
            }
        }
    }, [location.state]);

    // Separate useEffect to handle section analysis
    React.useEffect(() => {
        if (selectedSection && !sectionResults[selectedSection]) {
            console.log("Fetching document info for section:", selectedSection);
            fetchDocumentInfo(selectedSection);
        }
    }, [selectedSection]); // This will run whenever selectedSection changes

    // Add a debug log to track section selection
    React.useEffect(() => {
        console.log("Current section results:", sectionResults);
        console.log("Selected section:", selectedSection);
    }, [sectionResults, selectedSection]);

    // Helper function to render a status indicator
    const StatusIndicator = ({ status }) => {
        if (status === 'pending' || status === 'loading') {
            return (
                <div className="animate-pulse flex items-center">
                    <div className={`h-3 w-3 rounded-full ${status === 'pending' ? 'bg-gray-300' : 'bg-blue-500'} mr-2`}></div>
                    <span className="text-sm text-gray-500">{status === 'pending' ? 'Waiting...' : 'Loading...'}</span>
                </div>
            );
        } else if (status === 'completed') {
            return (
                <div className="flex items-center">
                    <div className="h-3 w-3 rounded-full bg-green-500 mr-2"></div>
                    <span className="text-sm text-green-600">Completed</span>
                </div>
            );
        } else {
            return (
                <div className="flex items-center">
                    <div className="h-3 w-3 rounded-full bg-red-500 mr-2"></div>
                    <span className="text-sm text-red-600">Failed</span>
                </div>
            );
        }
    };

    // Add function to fetch raw text
    const fetchRawText = async (sectionName) => {
        setLoadingRawText(true);
        console.log(`Fetching raw text for ${sectionName}`);
        try {
            const response = await fetch(`${baseUrl}/get-raw-section`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ 
                    filename: sectionName
                }),
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();
            console.log(`Raw text for ${sectionName}`, data);
            setRawText(data.content);
        } catch (err) {
            setError(`Failed to fetch raw text: ${err.message}`);
            logError(`Failed to fetch raw text for section ${sectionName}: ${err.message}`, 'App.jsx', { section_filename: sectionName, stack: err.stack });
        } finally {
            setLoadingRawText(false);
        }
    };

    // Modify the SectionSelector component
    const SectionSelector = () => {
        if (!sections.length) return null;

        // Get current section index from workflow state if it exists
        const savedWorkflowState = localStorage.getItem('workflowState');
        let currentSectionIndex = 0;
        if (savedWorkflowState) {
            const workflowState = JSON.parse(savedWorkflowState);
            currentSectionIndex = workflowState.currentSectionIndex || 0;
        }

        const handleSkipSection = () => {
            const nextSectionIndex = currentSectionIndex + 1;
            if (nextSectionIndex < sections.length) {
                // Get current workflow state
                const currentWorkflowState = JSON.parse(localStorage.getItem('workflowState') || '{}');
                
                // Update workflow state for next section
                const updatedWorkflowState = {
                    ...currentWorkflowState,
                    currentSectionIndex: nextSectionIndex,
                    currentPropertyIndex: 0,
                    documentFilename: sections[nextSectionIndex]
                };

                // Store updated workflow state
                localStorage.setItem('workflowState', JSON.stringify(updatedWorkflowState));

                // Set the new section and fetch its info
                setSelectedSection(sections[nextSectionIndex]);
                fetchDocumentInfo(sections[nextSectionIndex]);
            }
        };

        const handleSectionClick = (section) => {
            setSelectedRawSection(section);
            fetchRawText(section);
        };

        return (
            <div className="mb-4">
                <div className="flex justify-between items-center mb-2">
                    <label className="block text-sm font-medium text-gray-700">
                        Document Sections
                    </label>
                    {currentSectionIndex < sections.length - 1 && (
                        <button
                            onClick={handleSkipSection}
                            className="px-3 py-1 text-sm text-blue-600 hover:text-blue-800 border border-blue-600 rounded hover:bg-blue-50"
                        >
                            Skip to Next Section
                        </button>
                    )}
                </div>
                <div className="space-y-2">
                    {sections.map((section, index) => {
                    // Extract section name if it contains an underscore
                    const displayName = section.includes('_') 
                        ? section.split('_')[1].replace('.txt', '') 
                        : section;

                    return (
                        <div 
                            key={section}
                            className={`p-3 rounded-md border ${
                                index === currentSectionIndex 
                                    ? 'bg-blue-50 border-blue-300' 
                                    : 'bg-gray-50 border-gray-200'
                            }`}
                        >
                            <div className="flex items-center justify-between">
                                <button
                                    onClick={() => handleSectionClick(section)}
                                    className={`text-sm text-left hover:text-blue-600 ${
                                        index === currentSectionIndex 
                                            ? 'text-blue-700 font-medium' 
                                            : 'text-gray-600'
                                    }`}
                                >
                                    Section {index + 1} of {sections.length}: {displayName}
                                </button>
                                {index === currentSectionIndex && (
                                    <span className="text-xs text-blue-600 bg-blue-100 px-2 py-1 rounded">
                                        Current Section
                                    </span>
                                )}
                            </div>
                        </div>
                    );})}
                </div>

                {/* Raw Text Modal */}
                {selectedRawSection && (
                    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
                        <div className="bg-white rounded-lg w-full max-w-4xl flex flex-col" style={{ height: 'calc(100vh - 2rem)' }}>
                            {/* Header - Fixed */}
                            <div className="flex justify-between items-center p-4 border-b border-gray-200">
                                <h3 className="text-lg font-medium">
                                    Raw Text - Section {sections.indexOf(selectedRawSection) + 1}
                                </h3>
                                <button
                                    onClick={() => {
                                        setSelectedRawSection(null);
                                        setRawText(null);
                                    }}
                                    className="text-gray-500 hover:text-gray-700"
                                >
                                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                                    </svg>
                                </button>
                            </div>

                            {/* Section Info - Fixed */}
                            <div className="px-4 py-2 bg-gray-50 border-b border-gray-200">
                                <p className="text-sm text-gray-600">
                                    {sections.indexOf(selectedRawSection) + 1} of {sections.length}: {selectedRawSection.includes('_') 
                                        ? selectedRawSection.split('_')[1].replace('.txt', '') 
                                        : selectedRawSection}
                                </p>
                            </div>

                            {/* Content - Scrollable */}
                            <div className="flex-1 min-h-0 bg-white">
                                {loadingRawText ? (
                                    <div className="flex justify-center items-center h-full">
                                        <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"></div>
                                    </div>
                                ) : (
                                    <div className="h-full overflow-y-auto p-4">
                                        <pre className="whitespace-pre-wrap text-sm font-mono leading-relaxed">
                                            {rawText || 'No text available'}
                                        </pre>
                                    </div>
                                )}
                            </div>
                        </div>
                    </div>
                )}
            </div>
        );
    };

    const renderPropertyList = (sectionData) => {
        if (!sectionData || !sectionData.property_names) {
            return <p className="text-gray-500">No properties available</p>;
        }
        
        return (
            <div className="space-y-2">
                {sectionData.property_names.map((property, index) => (
                    <div 
                        key={index} 
                        className={`flex items-center gap-2 cursor-pointer p-2 rounded transition-colors ${
                            searchText === property 
                                ? searchStatus === 'success' 
                                    ? 'bg-green-100 border border-green-300' 
                                    : searchStatus === 'not-found' 
                                        ? 'bg-red-100 border border-red-300'
                                        : 'bg-blue-100 border border-blue-300'
                                : 'hover:bg-blue-50'
                        }`}
                        onClick={() => handlePropertyClick(property)}
                    >
                        <span>{property}</span>
                        {searchText === property && searchStatus === 'searching' && (
                            <div className="ml-auto animate-spin h-4 w-4 border-t-2 border-b-2 border-blue-500 rounded-full"></div>
                        )}
                        {searchText === property && searchStatus === 'success' && (
                            <div className="ml-auto text-green-600">✓</div>
                        )}
                        {searchText === property && searchStatus === 'not-found' && (
                            <div className="ml-auto text-red-600">✗</div>
                        )}
                    </div>
                ))}
            </div>
        );
    };

    const renderOverarchingPeriod = (sectionData) => {
        if (!sectionData || !sectionData.overarchingPeriod) {
            return <p className="text-gray-500">No overarching period available</p>;
        }
        const { start_date, end_date } = sectionData.overarchingPeriod;
        return (
            <div className="space-y-2">
                {[start_date, end_date].map((date, idx) => (
                    <div
                        key={idx}
                        className={`flex items-center gap-2 cursor-pointer p-2 rounded transition-colors ${
                            searchText === date
                                ? searchStatus === 'success'
                                    ? 'bg-green-100 border border-green-300'
                                    : searchStatus === 'not-found'
                                        ? 'bg-red-100 border border-red-300'
                                        : 'bg-blue-100 border border-blue-300'
                                : 'hover:bg-blue-50'
                        }`}
                        onClick={() => handlePropertyClick(date)}
                    >
                        <span>{idx === 0 ? 'Start Date: ' : 'End Date: '}{date ? formatDateToDDMMYYYY(date) : <span className="text-gray-400">(empty)</span>}</span>
                        {searchText === date && searchStatus === 'searching' && (
                            <div className="ml-auto animate-spin h-4 w-4 border-t-2 border-b-2 border-blue-500 rounded-full"></div>
                        )}
                        {searchText === date && searchStatus === 'success' && (
                            <div className="ml-auto text-green-600">✓</div>
                        )}
                        {searchText === date && searchStatus === 'not-found' && (
                            <div className="ml-auto text-red-600">✗</div>
                        )}
                    </div>
                ))}
            </div>
        );
    };

    const renderPeriodList = (sectionData) => {
        if (!sectionData || !sectionData.periods || !sectionData.periods.periods) {
            return <p className="text-gray-500">No periods available</p>;
        }
        return (
            <div className="space-y-2">
                {sectionData.periods.periods.map((period, idx) => (
                    <div
                        key={idx}
                        className={`flex items-center gap-2 cursor-pointer p-2 rounded transition-colors ${
                            searchText === period
                                ? searchStatus === 'success'
                                    ? 'bg-green-100 border border-green-300'
                                    : searchStatus === 'not-found'
                                        ? 'bg-red-100 border border-red-300'
                                        : 'bg-blue-100 border border-blue-300'
                                : 'hover:bg-blue-50'
                        }`}
                        onClick={() => handlePropertyClick(period)}
                    >
                        <span>{period}</span>
                        {searchText === period && searchStatus === 'searching' && (
                            <div className="ml-auto animate-spin h-4 w-4 border-t-2 border-b-2 border-blue-500 rounded-full"></div>
                        )}
                        {searchText === period && searchStatus === 'success' && (
                            <div className="ml-auto text-green-600">✓</div>
                        )}
                        {searchText === period && searchStatus === 'not-found' && (
                            <div className="ml-auto text-red-600">✗</div>
                        )}
                    </div>
                ))}
            </div>
        );
    };

    const renderRateTypeList = (sectionData) => {
        if (!sectionData || !sectionData.rateTypes || !sectionData.rateTypes.distinct_weekly_rates) {
            return <p className="text-gray-500">No rate types available</p>;
        }
        return (
            <div className="space-y-2">
                {sectionData.rateTypes.distinct_weekly_rates.map((rateType, idx) => (
                    <div
                        key={idx}
                        className={`flex items-center gap-2 cursor-pointer p-2 rounded transition-colors ${
                            searchText === rateType
                                ? searchStatus === 'success'
                                    ? 'bg-green-100 border border-green-300'
                                    : searchStatus === 'not-found'
                                        ? 'bg-red-100 border border-red-300'
                                        : 'bg-blue-100 border border-blue-300'
                                : 'hover:bg-blue-50'
                        }`}
                        onClick={() => handlePropertyClick(rateType)}
                    >
                        <span>{rateType}</span>
                        {searchText === rateType && searchStatus === 'searching' && (
                            <div className="ml-auto animate-spin h-4 w-4 border-t-2 border-b-2 border-blue-500 rounded-full"></div>
                        )}
                        {searchText === rateType && searchStatus === 'success' && (
                            <div className="ml-auto text-green-600">✓</div>
                        )}
                        {searchText === rateType && searchStatus === 'not-found' && (
                            <div className="ml-auto text-red-600">✗</div>
                        )}
                    </div>
                ))}
            </div>
        );
    };

    // Modify the renderEndpointResult to use sectionResults
    const renderEndpointResult = (endpointName) => {
                // Special handling for isValid as it might not exist in sectionResults initially
        if (endpointName === 'isValid') {
            const sectionIsValidData = sectionResults[selectedSection] && sectionResults[selectedSection].isValid;

            if (editingValidity) {
                return (
                    <React.Fragment>
                        <div className="flex items-center gap-4 my-2">
                            <label className="flex items-center">
                                <input
                                    type="radio"
                                    name="validity"
                                    checked={currentValidityData.valid === true}
                                    onChange={() => handleValidityChange(true)}
                                    className="form-radio h-4 w-4 text-blue-600"
                                />
                                <span className="ml-2 text-sm">Valid</span>
                            </label>
                            <label className="flex items-center">
                                <input
                                    type="radio"
                                    name="validity"
                                    checked={currentValidityData.valid === false}
                                    onChange={() => handleValidityChange(false)}
                                    className="form-radio h-4 w-4 text-red-600"
                                />
                                <span className="ml-2 text-sm">Invalid</span>
                            </label>
                        </div>
                        <div className="flex justify-end gap-2 mt-2">
                            <button
                                onClick={() => setEditingValidity(false)}
                                className="px-3 py-1 text-sm text-gray-600 hover:text-gray-800"
                            >
                                Cancel
                            </button>
                            <button
                                onClick={handleValiditySave}
                                className="px-3 py-1 text-sm bg-green-500 text-white rounded hover:bg-green-600"
                            >
                                Save
                            </button>
                        </div>
                    </React.Fragment>
                );
            } else {
                return (
                    <div className="flex items-center justify-between">
                        {sectionIsValidData === undefined || sectionIsValidData === null ? (
                            <p className="text-gray-500">No data - Edit to set</p>
                        ) : (
                            <p className={`text-lg ${sectionIsValidData.valid ? 'text-green-600' : 'text-red-600'}`}>
                                {sectionIsValidData.valid ? 'Valid Section' : 'Invalid Section'}
                            </p>
                        )}
                        <button
                            onClick={handleValidityEdit}
                            className="px-3 py-1 text-sm text-blue-500 hover:text-blue-700"
                        >
                            Edit
                        </button>
                    </div>
                );
            }
        }
        if (!selectedSection || !sectionResults[selectedSection] || !sectionResults[selectedSection][endpointName]) {
            // Allow editing for isValid even if it's not in endpointStatus yet
            if (endpointName === 'isValid' && selectedSection) {
                 // This case should be covered by the specific 'isValid' block above now.
                 // However, keeping a fallback or refining logic.
                 // If endpointStatus.isValid doesn't exist, but we want to allow setting it.
                 if (!endpointStatus.isValid) {
                    return (
                        <div className="flex items-center justify-between">
                             <p className="text-gray-500">No data - Edit to set</p>
                             <button
                                onClick={handleValidityEdit}
                                className="px-3 py-1 text-sm text-blue-500 hover:text-blue-700"
                            >
                                Edit
                            </button>
                        </div>
                    );
                 }
            }
            return <p className="text-gray-500">Waiting for data...</p>;
        }

        const sectionData = sectionResults[selectedSection][endpointName];

        switch (endpointName) {
            // isValid case is now handled above
            case 'propertyNames':
                return (
                    <div className="space-y-4">
                        {editingProperties ? (
                            <React.Fragment>
                                <div className="space-y-2">
                                    {propertyList.map((property, index) => (
                                        <div key={index} className="flex items-center gap-2">
                                            <input
                                                type="text"
                                                value={property}
                                                onChange={(e) => handlePropertyChange(index, e.target.value)}
                                                className="flex-grow px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                                                placeholder="Enter property name"
                                            />
                                            <button
                                                onClick={() => handlePropertyRemove(property)}
                                                className="text-red-500 hover:text-red-700"
                                            >
                                                ×
                                            </button>
                                        </div>
                                    ))}
                                </div>
                                <div className="flex gap-2 mt-2">
                                    <input
                                        type="text"
                                        value={newProperty}
                                        onChange={(e) => setNewProperty(e.target.value)}
                                        onKeyPress={handlePropertyKeyPress}
                                        placeholder="Add new property"
                                        className="flex-grow px-3 py-2 border rounded-lg"
                                    />
                                    <button
                                        onClick={handlePropertyAdd}
                                        className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600"
                                    >
                                        Add
                                    </button>
                                </div>
                                <div className="flex justify-end gap-2 mt-2">
                                    <button
                                        onClick={() => setEditingProperties(false)}
                                        className="px-4 py-2 text-gray-600 hover:text-gray-800"
                                    >
                                        Cancel
                                    </button>
                                    <button
                                        onClick={handlePropertySave}
                                        className="px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600"
                                    >
                                        Save
                                    </button>
                                </div>
                            </React.Fragment>
                        ) : (
                            <React.Fragment>
                                <div className="mb-2">
                                    {renderPropertyList(sectionResults[selectedSection].propertyNames)}
                                </div>
                                <div className="flex justify-end mt-4">
                                    <button
                                        onClick={handlePropertyEdit}
                                        className="px-4 py-2 text-blue-500 hover:text-blue-700"
                                    >
                                        Edit
                                    </button>
                                </div>
                            </React.Fragment>
                        )}
                        
                    </div>
                );
            case 'overarchingPeriod':
                if (editingOverarchingPeriod) {
                    return (
                        <React.Fragment>
                            <div className="space-y-2 my-2">
                                <div>
                                    <label htmlFor="start_date" className="block text-sm font-medium text-gray-700">Start Date</label>
                                    <input
                                        type="date"
                                        id="start_date"
                                        value={currentOverarchingPeriodData.start_date || ''}
                                        onChange={(e) => handleOverarchingPeriodChange('start_date', e.target.value)}
                                        className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                                    />
                                </div>
                                <div>
                                    <label htmlFor="end_date" className="block text-sm font-medium text-gray-700">End Date</label>
                                    <input
                                        type="date"
                                        id="end_date"
                                        value={currentOverarchingPeriodData.end_date || ''}
                                        onChange={(e) => handleOverarchingPeriodChange('end_date', e.target.value)}
                                        className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                                    />
                                </div>
                            </div>
                            <div className="flex justify-end gap-2 mt-2">
                                <button
                                    onClick={() => setEditingOverarchingPeriod(false)}
                                    className="px-3 py-1 text-sm text-gray-600 hover:text-gray-800"
                                >
                                    Cancel
                                </button>
                                <button
                                    onClick={handleOverarchingPeriodSave}
                                    className="px-3 py-1 text-sm bg-green-500 text-white rounded hover:bg-green-600"
                                >
                                    Save
                                </button>
                            </div>
                        </React.Fragment>
                    );
                } else {
                    const periodData = sectionResults[selectedSection] && sectionResults[selectedSection].overarchingPeriod;
                    const displayValue = periodData && periodData.start_date && periodData.end_date
                        ? `${formatDateToDDMMYYYY(periodData.start_date)} to ${formatDateToDDMMYYYY(periodData.end_date)}`
                        : "No data - Edit to set";
                    return (
                        <div className="flex items-center justify-between">
                             {renderOverarchingPeriod(sectionResults[selectedSection])}
                            <button
                                onClick={handleOverarchingPeriodEdit}
                                className="px-3 py-1 text-sm text-blue-500 hover:text-blue-700"
                            >
                                Edit
                            </button>
                        </div>
                    );
                   
                }
            case 'periods':
                return (
                    <div className="space-y-4">
                        {editingPeriods ? (
                            <React.Fragment>
                                <div className="space-y-2">
                                    {periodList.map((period, index) => (
                                        <div key={index} className="flex items-center gap-2">
                                            <input
                                                type="text"
                                                value={period}
                                                onChange={(e) => handlePeriodChange(index, e.target.value)}
                                                className="flex-grow px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                                                placeholder="Enter period name"
                                            />
                                            <button
                                                onClick={() => handlePeriodRemove(period)}
                                                className="text-red-500 hover:text-red-700"
                                            >
                                                ×
                                            </button>
                                        </div>
                                    ))}
                                </div>
                                <div className="flex gap-2 mt-2">
                                    <input
                                        type="text"
                                        value={newPeriod}
                                        onChange={(e) => setNewPeriod(e.target.value)}
                                        onKeyPress={handlePeriodKeyPress}
                                        placeholder="Add new period"
                                        className="flex-grow px-3 py-2 border rounded-lg"
                                    />
                                    <button
                                        onClick={handlePeriodAdd}
                                        className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600"
                                    >
                                        Add
                                    </button>
                                </div>
                                <div className="flex justify-end gap-2 mt-2">
                                    <button
                                        onClick={() => setEditingPeriods(false)}
                                        className="px-4 py-2 text-gray-600 hover:text-gray-800"
                                    >
                                        Cancel
                                    </button>
                                    <button
                                        onClick={handlePeriodSave}
                                        className="px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600"
                                    >
                                        Save
                                    </button>
                                </div>
                            </React.Fragment>
                        ) : (
                            <React.Fragment>
                                <div className="space-y-2">
                                    {renderPeriodList(sectionResults[selectedSection])}
                                </div>
                                <div className="flex justify-end mt-4">
                                    <button
                                        onClick={handlePeriodEdit}
                                        className="px-4 py-2 text-blue-500 hover:text-blue-700"
                                    >
                                        Edit
                                    </button>
                                </div>
                            </React.Fragment>
                        )}
                    </div>
                );
            case 'rateTypes':
                return (
                    <div className="space-y-4">
                        {editingRateTypes ? (
                            <React.Fragment>
                                <div className="space-y-2">
                                    {rateTypeList.map((rateType, index) => (
                                        <div key={index} className="flex items-center gap-2">
                                            <input
                                                type="text"
                                                value={rateType}
                                                onChange={(e) => handleRateTypeChange(index, e.target.value)}
                                                className="flex-grow px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                                                placeholder="Enter rate type name"
                                            />
                                            <button
                                                onClick={() => handleRateTypeRemove(rateType)}
                                                className="text-red-500 hover:text-red-700"
                                            >
                                                ×
                                            </button>
                                        </div>
                                    ))}
                                </div>
                                <div className="flex gap-2 mt-2">
                                    <input
                                        type="text"
                                        value={newRateType}
                                        onChange={(e) => setNewRateType(e.target.value)}
                                        onKeyPress={handleRateTypeKeyPress}
                                        placeholder="Add new rate type"
                                        className="flex-grow px-3 py-2 border rounded-lg"
                                    />
                                    <button
                                        onClick={handleRateTypeAdd}
                                        className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600"
                                    >
                                        Add
                                    </button>
                                </div>
                                <div className="flex justify-end gap-2 mt-2">
                                    <button
                                        onClick={() => setEditingRateTypes(false)}
                                        className="px-4 py-2 text-gray-600 hover:text-gray-800"
                                    >
                                        Cancel
                                    </button>
                                    <button
                                        onClick={handleRateTypeSave}
                                        className="px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600"
                                    >
                                        Save
                                    </button>
                                </div>
                            </React.Fragment>
                        ) : (
                            <React.Fragment>
                                <div className="space-y-2">
                                    {renderRateTypeList(sectionResults[selectedSection])}
                                </div>
                                <div className="flex justify-end mt-4">
                                    <button
                                        onClick={handleRateTypeEdit}
                                        className="px-4 py-2 text-blue-500 hover:text-blue-700"
                                    >
                                        Edit
                                    </button>
                                </div>
                            </React.Fragment>
                        )}
                    </div>
                );
            case 'hasSTO':
                if (editingSTOInfo) {
                    return (
                        <React.Fragment>
                            <div className="flex items-center gap-4 my-2">
                                <label className="flex items-center">
                                    <input
                                        type="radio"
                                        name="stoStatus"
                                        checked={currentSTOInfoData.has_sto === true}
                                        onChange={() => handleSTOInfoChange(true)}
                                        className="form-radio h-4 w-4 text-blue-600"
                                    />
                                    <span className="ml-2 text-sm">Yes</span>
                                </label>
                                <label className="flex items-center">
                                    <input
                                        type="radio"
                                        name="stoStatus"
                                        checked={currentSTOInfoData.has_sto === false}
                                        onChange={() => handleSTOInfoChange(false)}
                                        className="form-radio h-4 w-4 text-red-600"
                                    />
                                    <span className="ml-2 text-sm">No</span>
                                </label>
                            </div>
                            <div className="flex justify-end gap-2 mt-2">
                                <button
                                    onClick={() => setEditingSTOInfo(false)}
                                    className="px-3 py-1 text-sm text-gray-600 hover:text-gray-800"
                                >
                                    Cancel
                                </button>
                                <button
                                    onClick={handleSTOInfoSave}
                                    className="px-3 py-1 text-sm bg-green-500 text-white rounded hover:bg-green-600"
                                >
                                    Save
                                </button>
                            </div>
                        </React.Fragment>
                    );
                } else {
                    const stoData = sectionResults[selectedSection] && sectionResults[selectedSection].hasSTO;
                    const displayValue = stoData === undefined || stoData === null
                        ? "No data - Edit to set"
                        : stoData.has_sto ? 'Yes' : 'No';
                    return (
                        <div className="flex items-center justify-between">
                            <p>{displayValue}</p>
                            <button
                                onClick={handleSTOInfoEdit}
                                className="px-3 py-1 text-sm text-blue-500 hover:text-blue-700"
                            >
                                Edit
                            </button>
                        </div>
                    );
                }
            default:
                return <p>No data available</p>;
        }
    };

    return (
        <div className="min-h-screen bg-gray-100 py-6">
            <div className="container mx-auto px-4">
                <div className="flex items-center justify-center gap-3 mb-8">
                    <img src="/favicon.ico" alt="Document Processor Icon" className="w-8 h-8" />
                    <h1 className="text-3xl font-bold">Document Processor</h1>
                </div>
                
                {/* Add Model Selector */}
                <div className="mb-3">
                    <label htmlFor="modelSelect" className="form-label block text-sm font-medium text-gray-700 mb-1">Select Model:</label>
                    <select 
                        id="modelSelect" 
                        className="form-select mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" 
                        value={selectedModel} 
                        onChange={(e) => setSelectedModel(e.target.value)}
                    >
                        {modelOptions.map(option => (
                            <option key={option.value} value={option.value}>
                                {option.label}
                            </option>
                        ))}
                    </select>
                </div>

                {error && (
                    <div className="mb-8 p-4 bg-red-100 border border-red-400 text-red-700 rounded" style={{ position: 'relative' }}>
                        {error}
                        {error.includes('Please reselect your PDF files') && (
                            <button
                                onClick={() => {
                                    setError(null);
                                    setUserDismissedReselectionError(true);
                                    localStorage.removeItem('pendingFiles');
                                    localStorage.removeItem('workflowState');
                                    localStorage.removeItem('processNextFile');
                                    setFile(null);
                                    setFiles([]);
                                    setPdfUrl(null);
                                    setSelectedSection(null);
                                    setSections([]);
                                    setSectionResults({});
                                    // Clear the navigation state by replacing the current history entry
                                    window.history.replaceState({}, '', window.location.pathname);
                                }}
                                style={{
                                    position: 'absolute',
                                    top: '8px',
                                    right: '8px',
                                    width: '20px',
                                    height: '20px',
                                    display: 'flex',
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                    backgroundColor: 'transparent',
                                    border: 'none',
                                    borderRadius: '50%',
                                    cursor: 'pointer',
                                    fontSize: '16px',
                                    fontWeight: 'bold',
                                    color: '#dc2626'
                                }}
                                title="Clear and start fresh"
                                onMouseEnter={(e) => e.target.style.backgroundColor = '#fecaca'}
                                onMouseLeave={(e) => e.target.style.backgroundColor = 'transparent'}
                            >
                                ×
                            </button>
                        )}
                    </div>
                )}
                
                {/* Main Content Area - Two Column Layout */}
                <div className="grid grid-cols-1 lg:grid-cols-5 gap-8 items-stretch">
                    {/* Left Column - Controls and Analysis Results */}
                    <div className="lg:col-span-2 flex flex-col gap-6 h-full">
                        {/* File Upload Controls */}
                        <div className="bg-white p-6 rounded-lg shadow-md">
                            <h2 className="text-xl font-semibold mb-4">Upload Document</h2>
                            <form onSubmit={handleSubmit} className="space-y-4">
                                <div className="flex flex-col items-center">
                                    <label className="w-full flex flex-col items-center px-4 py-6 bg-white text-blue-500 rounded-lg shadow-lg tracking-wide uppercase border border-blue-500 cursor-pointer hover:bg-blue-500 hover:text-white">
                                        <svg className="w-8 h-8" fill="currentColor" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
                                            <path d="M16.88 9.1A4 4 0 0 1 16 17H5a5 5 0 0 1-1-9.9V7a3 3 0 0 1 4.52-2.59A4.98 4.98 0 0 1 17 8c0 .38-.04.74-.12 1.1zM11 11h3l-4-4-4 4h3v3h2v-3z" />
                                        </svg>
                                        <span className="mt-2 text-base leading-normal">Select PDF file(s)</span>
                                        <input type='file' className="hidden" onChange={handleFileChange} accept=".pdf" multiple />
                                    </label>
                                    {files.length > 0 && (
                                        <div className="mt-2 text-sm text-gray-500 w-full">
                                            <p className="font-semibold">Selected files ({files.length}):</p>
                                            <ul className="list-disc pl-5 mt-1">
                                                {files.map((f, index) => (
                                                    <li key={index}>{f.name}</li>
                                                ))}
                                            </ul>
                                        </div>
                                    )}
                                    {file && files.length === 0 && <p className="mt-2 text-sm text-gray-500 w-full text-center">Selected: {file.name}</p>}
                                </div>
                                <div className="flex justify-center gap-4">
                                    <button
                                        type="submit"
                                        disabled={!(file || files.length > 0) || processing}
                                        className={`w-full px-4 py-3 text-white font-semibold rounded-lg shadow-md 
                                            ${!(file || files.length > 0) || processing 
                                                ? 'bg-gray-400 cursor-not-allowed' 
                                                : 'bg-blue-500 hover:bg-blue-700'}`}
                                    >
                                        {processing ? 'Processing...' : 'Analyze Document'}
                                    </button>
                                    <button
                                        onClick={handleAutoProcess}
                                        disabled={!(file || files.length > 0) || autoProcessing}
                                        className={`w-full px-4 py-3 text-white font-semibold rounded-lg shadow-md
                                            ${!(file || files.length > 0) || autoProcessing
                                                ? 'bg-gray-400 cursor-not-allowed'
                                                : 'bg-green-500 hover:bg-green-700'}`}
                                    >
                                        {autoProcessing ? `Processing... (${taskStatus || ''})` : files.length > 1 ? 'Process All Files' : 'Process Automatically'}
                                    </button>
                                </div>
                            </form>
                        </div>
                        
                        {/* Analysis Results */}
                        <div className="bg-white p-6 rounded-lg shadow-md flex-grow">
                            <h2 className="text-xl font-semibold mb-4">Document Analysis Results</h2>
                            
                            {sections.length > 0 && <SectionSelector />}
                            
                            {!selectedSection && !processing && Object.keys(endpointStatus).length === 0 && (
                                <p className="text-gray-500">Upload and analyze a document to see results</p>
                            )}
                            
                            {selectedSection && Object.keys(endpointStatus).length > 0 && (
                                <div className="space-y-4">
                                    {Object.entries(endpointStatus).map(([name, { status, label }]) => {
                                        return (
                                            <div key={name} className="p-4 bg-blue-50 rounded-lg">
                                                <div className="flex justify-between items-center mb-2">
                                                    <p className="font-medium">{label}</p>
                                                    <StatusIndicator status={status} />
                                                </div>
                                                {renderEndpointResult(name)}
                                            </div>
                                        );
                                    })}
                                </div>
                            )}
                        </div>
                        
                        {/* Process Properties Button */}
                        {selectedSection && // Ensure a section is selected
                        sectionResults[selectedSection] && sectionResults[selectedSection].isValid && sectionResults[selectedSection].isValid.valid && // Ensure the section is valid
                        !editingProperties && // Ensure user isn't editing properties
                        propertyList.length > 0 && ( // Ensure properties exist
                            <div className="mt-6"> {/* Added margin-top */}
                                <button
                                    onClick={handlePropertyAnalysis} 
                                    className="w-full px-4 py-3 bg-green-600 text-white font-semibold rounded-lg shadow-md hover:bg-green-700" // Adjusted colors slightly
                                >
                                    Next: Start Property Processing {/* New Text */}
                                </button>
                            </div>
                        )}
                    </div>
                    
                    {/* Right Column - PDF Viewer */}
                    <div className="lg:col-span-3">
                        <div className="bg-white p-6 rounded-lg shadow-md flex flex-col"
                            style={{ position: 'sticky', top: '2rem', maxHeight: 'calc(100vh - 4rem)' }}>
                            <h2 className="text-xl font-semibold mb-4">PDF Viewer</h2>
                            <div className="flex-1 min-h-[400px] max-h-full overflow-auto">
                                {(() => {
                                    console.log('PDF Viewer render - pdfUrl:', pdfUrl);
                                    return pdfUrl ? (
                                        <EnhancedPdfViewer
                                            url={pdfUrl}
                                            searchText={searchText}
                                            onSearchResult={handleSearchResult}
                                        />
                                    ) : (
                                        <div className="flex items-center justify-center h-full text-gray-500">
                                            Please upload a PDF document to view it.
                                        </div>
                                    );
                                })()}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
}

// Wrap the App with Router
function AppWithRouter() {
    return (
        <Router>
            <Routes>
                <Route path="/" element={<App />} />
                <Route path="/property-analysis" element={<PropertyAnalysis />} />
                <Route path="/room-type-analysis" element={<RoomTypeAnalysis />} />
            </Routes>
        </Router>
    );
}

export default AppWithRouter;